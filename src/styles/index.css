@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Dark Theme (Default and Only Theme) */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-tertiary: #71717a;
    --border: #27272a;
    --accent-start: #8b5cf6;
    --accent-end: #d946ef;
    --accent-hover: #7c3aed;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --shadow: rgba(0, 0, 0, 0.3);
  }

  body {
    @apply bg-bgPrimary text-textPrimary transition-colors duration-300;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  }

  * {
    @apply transition-colors duration-200;
  }
}

/* Custom scrollbar */
@layer components {
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-bgSecondary;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full hover:bg-textTertiary;
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .platform-card {
    @apply relative w-icon h-icon rounded-2xl border cursor-pointer transition-all duration-300 flex items-center justify-center overflow-hidden;
    background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    border: 1px solid rgba(255,255,255,0.1);
  }

  .platform-card:hover {
    @apply scale-110 shadow-lg;
    border-color: var(--accent-start);
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
  }

  .platform-card.selected {
    @apply scale-105;
    background: linear-gradient(145deg, var(--accent-start), var(--accent-end));
    border-color: var(--accent-end);
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4);
  }

  /* New expandable platform card styles */
  .platform-card-expandable {
    @apply relative h-icon rounded-2xl border cursor-pointer transition-all duration-300 flex items-center overflow-hidden px-4;
    background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    border: 1px solid rgba(255,255,255,0.1);
    width: 100%;
  }

  .platform-card-expandable:hover {
    @apply shadow-lg;
    border-color: var(--accent-start);
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
  }

  .platform-card-expandable.selected {
    background: linear-gradient(145deg, var(--accent-start), var(--accent-end));
    border-color: var(--accent-end);
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4);
  }

  .platform-card-expandable.expanded {
    @apply shadow-xl;
    border-color: var(--accent-start);
    box-shadow: 0 12px 40px rgba(99, 102, 241, 0.4);
  }
}
