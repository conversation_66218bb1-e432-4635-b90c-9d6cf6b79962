import { platforms } from '../data/platforms';
import { useAppStore } from '../store';
import { useState } from 'react';

export const Sidebar = () => {
  const { activeMode, activePlatform, setPlatform } = useAppStore();
  const [hoveredPlatform, setHoveredPlatform] = useState<string | null>(null);
  const list = platforms.filter((p) => p.mode === activeMode);

  return (
    <aside className={`bg-bgSecondary border-r border-border flex flex-col py-6 space-y-3 scrollbar-thin overflow-y-auto transition-all duration-300 ${
      hoveredPlatform ? 'w-48' : 'w-16'
    }`}>
      {list.length === 0 ? (
        <div className="text-textTertiary text-xs text-center px-2">
          No platforms available
        </div>
      ) : (
        list.map((p) => {
          const isSelected = activePlatform?.id === p.id;
          const isHovered = hoveredPlatform === p.id;

          return (
            <div
              key={p.id}
              onClick={() => setPlatform(p)}
              onMouseEnter={() => setHoveredPlatform(p.id)}
              onMouseLeave={() => setHoveredPlatform(null)}
              className="group relative cursor-pointer flex items-center pl-2"
            >
              <div className="relative flex items-center">
                {/* Icon Box */}
                <div className={`platform-card-expandable ${isSelected ? 'selected' : ''} ${
                  isHovered ? 'expanded' : ''
                }`}>
                  {/* Icon - Centered in box */}
                  <img
                    src={`/icons/${p.iconFile}`}
                    alt={p.name}
                    className={`w-5 h-5 transition-all duration-300 ${
                      isSelected ? 'drop-shadow-lg' : ''
                    } ${isHovered ? 'scale-110' : ''}`}
                  />

                  {/* Selection Indicator */}
                  {isSelected && (
                    <div className="absolute -right-1.5 top-1/2 -translate-y-1/2 w-2 h-6 bg-accentStart rounded-full border-2 border-bgSecondary" />
                  )}
                </div>

                {/* Text Outside Icon Box */}
                <span className={`ml-3 text-textPrimary font-medium text-sm whitespace-nowrap transition-all duration-300 ${
                  hoveredPlatform ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4 pointer-events-none'
                }`}>
                  {p.name}
                </span>
              </div>
            </div>
          );
        })
      )}
    </aside>
  );
};
