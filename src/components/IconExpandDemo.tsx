import { useState } from 'react';

const demoIcons = [
  { id: 'chat', name: 'ChatGPT', iconFile: 'chatgpt.svg' },
  { id: 'claude', name: '<PERSON>', iconFile: 'claude.svg' },
  { id: 'midjourney', name: 'Midjourney', iconFile: 'midjourney.svg' },
  { id: 'github', name: 'GitHub Copilot', iconFile: 'github.svg' },
];

export const IconExpandDemo = () => {
  const [hoveredIcon, setHoveredIcon] = useState<string | null>(null);

  return (
    <div className="p-8 bg-bgPrimary min-h-screen">
      <h1 className="text-2xl font-bold text-textPrimary mb-8">Icon Expand Demo</h1>
      
      <div className="space-y-4">
        {demoIcons.map((icon) => {
          const isHovered = hoveredIcon === icon.id;
          
          return (
            <div
              key={icon.id}
              onMouseEnter={() => setHoveredIcon(icon.id)}
              onMouseLeave={() => setHoveredIcon(null)}
              className={`cursor-pointer transition-all duration-300 ${
                isHovered ? 'w-64' : 'w-14'
              }`}
            >
              <div className={`platform-card-expandable ${isHovered ? 'expanded' : ''}`}>
                {/* Unified Icon Background */}
                <div className={`absolute inset-0 bg-gradient-to-br from-[#898a7f] to-[#a8a99e] opacity-25 hover:opacity-35 transition-opacity duration-300 rounded-2xl`} />

                {/* Icon Container */}
                <div className="flex items-center space-x-3 relative z-10 w-full">
                  {/* Icon */}
                  <div className={`w-8 h-8 bg-gradient-to-br from-accentStart to-accentEnd rounded-lg flex items-center justify-center flex-shrink-0 transition-all duration-300 ${
                    isHovered ? 'scale-110' : ''
                  }`}>
                    <span className="text-white font-bold text-sm">
                      {icon.name.charAt(0)}
                    </span>
                  </div>

                  {/* Expandable Name */}
                  <span className={`text-textPrimary font-medium text-sm whitespace-nowrap transition-all duration-300 ${
                    isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4 pointer-events-none'
                  }`}>
                    {icon.name}
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="mt-8 text-textSecondary text-sm">
        <p>Hover over the icons above to see them expand and show their names!</p>
        <p className="mt-2">This demonstrates the expandable icon functionality with smooth transitions.</p>
      </div>
    </div>
  );
};
