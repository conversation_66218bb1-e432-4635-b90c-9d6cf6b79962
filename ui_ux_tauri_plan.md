```markdown
# Sidebar Upgrade Plan

---

## Step 1: Adjust Sidebar Expansion Width

**File:** `src/components/Sidebar.tsx`

```tsx
<aside
  className={`bg-bgSecondary border-r border-border flex flex-col items-center py-6 space-y-3 scrollbar-thin overflow-y-auto transition-all duration-300 ${
    hoveredPlatform ? 'w-56' : 'w-sidebar'
  }`}
>
```

---

## Step 2: Equalize Icon Box Width and Height

**File:** `src/styles/index.css`

```css
.platform-card-expandable {
  @apply relative border cursor-pointer transition-all duration-300 flex items-center overflow-hidden;
  background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  border: 1px solid rgba(255,255,255,0.1);
  width: 40px;
  height: 40px;
  border-radius: 6px; /* minimal round corner */
}
```

---

## Step 3: Expand Icon Box on Hover

**File:** `src/components/Sidebar.tsx`

```tsx
<div
  className={`platform-card-expandable ${isSelected ? 'selected' : ''} ${
    isHovered ? 'expanded w-full' : ''
  }`}
>
```

---

## Step 4: Update Expanded State Styling

**File:** `src/styles/index.css`

```css
.platform-card-expandable.expanded {
  @apply shadow-xl flex pl-3;
  border-color: var(--accent-start);
  box-shadow: 0 12px 40px rgba(99, 102, 241, 0.4);
  width: 100%;
}
```

---

## Step 5: Show Icon + Name Together

**File:** `src/components/Sidebar.tsx`

```tsx
<div className="flex items-center space-x-2 relative z-10 w-full">
  <img
    src={`/icons/${p.iconFile}`}
    alt={p.name}
    className={`w-5 h-5 transition-all duration-300 flex-shrink-0 ${
      isSelected ? 'drop-shadow-lg' : ''
    } ${isHovered ? 'scale-110' : ''}`}
  />
  <span
    className={`text-textPrimary font-medium text-sm whitespace-nowrap transition-all duration-300 ${
      hoveredPlatform ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4 pointer-events-none'
    }`}
  >
    {p.name}
  </span>
</div>
```
```

